import {keys, set} from 'lodash';
import {Path} from '@panda-design/path-form';
import {BaseParam, Description, JSONSchema} from '@/types/mcp/mcp';

export interface TreeData {
    title: string;
    value: string;
    key: string;
    disabled?: boolean;
    bodyPath?: Path;
    children?: TreeData[];
}

export const getBodyTreeData = (list: BaseParam[], titleWidthRoot: boolean): TreeData[] => {
    return list.map(item => {
        return {
            title: titleWidthRoot ? item?.key : item?.name,
            value: item?.key,
            key: item?.fieldPath?.join('.'),
            bodyPath: item?.fieldPath,
            children: item?.children ? getBodyTreeData(item?.children, titleWidthRoot) : [],
        };
    });
};

// 用于提交表单时，构建请求体
export const fillParamValueToJSONProperties = (params: BaseParam[], jsonSchema: JSONSchema): JSONSchema => {
    params?.forEach(param => {
        const {value, children, fieldPath} = param;
        if (value) {
            const endPath = fieldPath.slice(1);
            const jsonPath = [];
            endPath.forEach(p => {
                jsonPath.push('properties');
                jsonPath.push(p);
            });
            jsonPath.push('value');
            set(
                jsonSchema,
                jsonPath,
                value
            );
        }
        if (children) {
            fillParamValueToJSONProperties(children, jsonSchema);
        }
    });
    return jsonSchema;
};

// 这个函数是为了将body中的数据转换为可以在table中渲染的树形数据
// eslint-disable-next-line max-len, complexity
export const transformBodySchemaToTableTreeData = (
    jsonSchema: JSONSchema | Description,
    parentPath: string[] = [],
    parent?: any
) => {
    if (!jsonSchema) {
        return undefined;
    }
    const handleParent = parent ?? [];
    if (jsonSchema.type === 'object') {
        const {properties, required} = jsonSchema as JSONSchema;
        const childrenKeys = keys(properties ?? {}) ?? [];
        for (let i = 0; i < childrenKeys?.length; i++) {
            const childKey = childrenKeys[i];
            const type = properties[childKey].type;
            const fieldPath = parentPath.length > 0 ? [...parentPath, childKey] : ['body', childKey];
            if (type === 'object' || type === 'array') {
                // currentKey是用来表示该数据的唯一性，给react渲染的。
                // fieldPath是用于表示这个表单项的路径，从表单设值取值时用的。path实际上就是原数据的key的拼接。
                const item: BaseParam & {children: BaseParam[]} = {
                    name: childKey,
                    key: fieldPath.join('.'),
                    ...properties[childKey],
                    fieldPath,
                    description: properties[childKey].description,
                    required: required?.includes(childKey),
                    children: [],
                };
                // 用户不用关心数组类型内部的结构，仅递归object类型即可
                // eslint-disable-next-line max-depth
                if (type === 'object') {
                    transformBodySchemaToTableTreeData(properties[childKey] as JSONSchema, fieldPath, item.children);
                }
                handleParent.push(item);
            } else {
                handleParent.push({
                    ...properties[childKey],
                    key: fieldPath.join('.'),
                    fieldPath,
                    name: childKey,
                    required: required?.includes(childKey),
                });
            }
        }
        return handleParent;
    } else {
        handleParent.children.push({
            ...jsonSchema,
        });
        return handleParent;
    }
};

/**
 * 处理除Body外的其它请求参数。
 * @param parameters 一个对象，键是header、query、path、cookie，值是参数列表
 */
export const transformRequestParamsToTableData = (parameters: Record<string, BaseParam[]>) => {
    return keys(parameters)?.reduce(
        (acc, cur) => {
            acc[cur] = parameters[cur]?.map(param => ({
                ...param,
                key: `${cur}.${param.name}`,
                fieldPath: [`${cur}`, `${param.name}`],
            }));
            return acc;
        },
        {} as Record<string, BaseParam[]>
    );
};
