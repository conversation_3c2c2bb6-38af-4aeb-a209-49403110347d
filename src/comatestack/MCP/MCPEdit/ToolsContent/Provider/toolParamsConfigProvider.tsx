/* eslint-disable max-lines */
import {Path} from '@panda-design/path-form';
import {FormInstance} from 'antd';
import constate from 'constate';
import {first, get, last, omit} from 'lodash';
import {useCallback, useRef} from 'react';
import {BaseParam, JSONSchema} from '@/types/mcp/mcp';


/**
 * created by zulu
 * 在树形数据中查找指定路径对应的下标路径
 * @param path - 要查找的路径，例如 ['parent', 'child']
 * @param data - 树形数据源，每个节点应包含key字段
 * @returns 匹配项的下标路径数组，例如 [0, 1] 表示第一层的第0个元素的children中的第1个元素
 */
export const findTreeItemIndexPath = (path: string[], data: any[]): number[] => {
    if (!data || !Array.isArray(data)) {
        return [];
    }

    const findIndexPath = (currentPath: string[], currentData: any[], currentIndexPath: number[]): number[] | null => {
        if (currentPath.length === 0) {
            return currentIndexPath;
        }

        const currentKey = currentPath[0];
        for (let i = 0; i < currentData.length; i++) {
            const item = currentData[i];
            if (item.name === currentKey) {
                const result = findIndexPath(
                    currentPath.slice(1),
                    item.children || [],
                    [...currentIndexPath, i]
                );
                if (result) {
                    return result;
                }
            }
        }
        return null;
    };

    const result = findIndexPath(path, data, []);
    return result || [];
};

interface Props{
    form: FormInstance;
}

const getParamsListPath = (activeToolIndex: number): Array<string|number> => {
    return ['tools', activeToolIndex, 'toolParams', 'toolParams'];
};

// const getParamPathInParamsList = (
//     activeToolIndex: number,
//     paramKey: string,
//     paramsList: BaseParam[]
// ): Array<string|number> => {
//     const paramsListPath = getParamsListPath(activeToolIndex);
//     const index = paramsList.findIndex(param => param.refParam === paramKey);
//     return [...paramsListPath, index];
// };

interface GetParamValueFieldPathProps{
    basePath: Path;
    record: Pick<BaseParam, 'fieldPath' | 'key'>;
    allParams: any;
}

export const getParamValueFieldPath = ({basePath, record, allParams}: GetParamValueFieldPathProps) => {
    const activeTab = first(record.fieldPath);
    const data = allParams?.[activeTab];
    if (data) {
        if (activeTab !== 'body') {
            const index = data.findIndex((item: BaseParam) => item.key === record.key);
            return [...basePath, index, 'value'];
        } else {
            const path = findTreeItemIndexPath(record.fieldPath.slice(1) as string[], data);
            const middlePath: Array<string|number> = [];
            path.forEach((p, i) => {
                if (i === path.length - 1) {
                    middlePath.push(p);
                } else {
                    middlePath.push(p, 'children');
                }
            });
            return [...basePath, ...middlePath, 'value'];
        }
    }
    return [...basePath, -1, 'value'];
};

const getParamValueFieldPathInConfig = (
    activeToolIndex: number,
    key: string,
    toolApiConfigData: any
) => {
    const paramsConfigTableTabKey: string = first(key?.split('.'));
    const path = getParamValueFieldPath({
        basePath: [
            'tools',
            activeToolIndex,
            'toolConf',
            'openapiConf',
            'parameters',
            paramsConfigTableTabKey,
        ],
        record: {
            fieldPath: key?.split('.') ?? [],
            key,
        },
        allParams: toolApiConfigData,
    });
    return path;
};

const getParamFieldPathInConfig = (
    activeToolIndex: number,
    key: string,
    toolApiConfigData: any
) => {
    const paramValueFieldPathInConfig = getParamValueFieldPathInConfig(activeToolIndex, key, toolApiConfigData);
    const paramFieldPathInConfig = paramValueFieldPathInConfig.slice(5, -1);
    return paramFieldPathInConfig;
};

/**
 * api配置中的参数默认值，如果以$开头，表示这个参数被添加到tool中，使用它判断tool中的参数是否完整，即除了被添加到tool的参数外，其它必填参数必须有用户指定的默认值。
 * 如果不以$开头，则代表改值为该api参数的常量值。此时，这个参数不需要向tool提供。故，也不可以将该值添加至tool。
 * 对于已添加至tool的参数，在api配置中不可修改其默认值。而在tool中修改参数名时，自动将参数名的改动映射到api配置中。
 * 例如：tool中参数名改为AAA，则自动将api配置中的参数默认值改为$AAA。
 */
const getDefaultValueWhenAddParamToTool = (name: string) => {
    return '$' + name;
};

const getNewParamValue = (refParam: string, record: BaseParam) => {
    return {
        refParam,
        ...omit(
            record,
            [
                'children',
                'currentPath',
                'key', 'properties',
                'x-iapi-ignore-properties',
                'x-iapi-orders',
                'x-iapi-refs',
            ]
        ),
        name: record.name,
        exampleValue: record.example,
    };
};

const getParamsTabKeyFromParamKey = (paramKey: string): string => {
    return first(paramKey?.split('.'));
};

const getRequestBodyJsonSchemaPath = (toolIndex: number) => {
    return ['tools', toolIndex, 'toolConf', 'openapiConf', 'requestBody', 'jsonSchema'];
};

const getOpenApiConfigParamsPath = (toolIndex: number) => {
    return ['tools', toolIndex, 'toolConf', 'openapiConf', 'parameters'];
};

const getParamNameFromParamKey = (paramKey: string): string => {
    return last(paramKey?.split('.'));
};

const getParamsConfigPath = (toolIndex: number) => {
    return [
        'tools',
        toolIndex,
        'toolConf',
        'openapiConf',
        'parameters',
    ];
};

// 将tool参数相关的业务逻辑提到这里，避免散在各个UI组件中
export const [ToolParamsConfigProvider, useToolParamsConfigContext] = constate(({form}: Props) => {
    const {setFieldValue, getFieldValue} = form;

    // 用toolParamsChanged处理一下setFieldValue无法触发onFieldChange带来的，无法判断用户有没有修改参数列表的问题
    const toolParamsChanged = useRef(false);
    const toolApiConfigDataRef = useRef(null);

    /**
     * paramKey用来是唯一用来标识参数的值。在参数列表中，它是refParams，在参数配置中，它是key。
     * toolIndex用来表示是哪一个tool上的参数。
     * 业务逻辑：每次移除一个参数时，都要删除在参数配置中默认写入的参数别名。
     */
    const removeToolParam = useCallback(
        (toolIndex: number, paramKey: string) => {
            const targetToolParamsListPath = getParamsListPath(toolIndex);
            const paramsList = getFieldValue(targetToolParamsListPath);
            // const targetParamPath = getParamPathInParamsList(toolIndex, paramKey, paramsList);
            const filteredParamsList = paramsList?.filter((param: BaseParam) => param.refParam !== paramKey);
            setFieldValue(targetToolParamsListPath, filteredParamsList);
            const paramValueFieldPathInConfigTable = getParamValueFieldPathInConfig(
                toolIndex,
                paramKey,
                toolApiConfigDataRef.current
            );
            setFieldValue(paramValueFieldPathInConfigTable, '');
            toolParamsChanged.current = true;
        },
        [getFieldValue, setFieldValue, toolApiConfigDataRef]
    );

    const addToolParam = useCallback(
        (toolIndex: number, record: BaseParam) => {
            const paramKey = record.key;
            const name = record.name;
            const paramValueFieldPathInConfigTable = getParamValueFieldPathInConfig(
                toolIndex,
                paramKey,
                toolApiConfigDataRef.current
            );
            const targetToolParamsListPath = getParamsListPath(toolIndex);
            const paramsList = getFieldValue(targetToolParamsListPath);
            // 加参数
            setFieldValue(
                paramValueFieldPathInConfigTable,
                getDefaultValueWhenAddParamToTool(name)
            );
            setFieldValue(
                targetToolParamsListPath,
                [
                    ...paramsList || [],
                    getNewParamValue(paramKey, record),
                ]
            );
            toolParamsChanged.current = true;
        },
        [getFieldValue, setFieldValue]
    );

    const syncParamNameFieldValueToConfigTable = useCallback(
        (toolIndex: number, paramKey: string, value: string) => {
            const paramValueFieldPathInConfigTable = getParamValueFieldPathInConfig(
                toolIndex,
                paramKey,
                toolApiConfigDataRef.current
            );
            setFieldValue(
                paramValueFieldPathInConfigTable,
                value ? '$' + value : ''
            );
        },
        [setFieldValue]
    );

    // 批量设置时需不改变已填入的参数，merge一下
    const batchSetToolParams = useCallback(
        (toolIndex: number, paramKeys: string[]) => {
            const targetToolParamsListPath = getParamsListPath(toolIndex);
            const paramsList = getFieldValue(targetToolParamsListPath);
            const unRelatedParamKeys: string[] = [];
            const mergedParamsList = paramKeys.map(key => {
                const existParam = paramsList?.find((item: BaseParam) => item?.refParam === key);
                if (existParam) {
                    return existParam;
                } else {
                    unRelatedParamKeys.push(key);
                    const jsonSchema: JSONSchema = getFieldValue(getRequestBodyJsonSchemaPath(toolIndex));
                    const paramsTabKey = getParamsTabKeyFromParamKey(key);
                    if (paramsTabKey === 'body') {
                        const bodyParamPath = key?.split('.')?.reduce(
                            (acc, cur, index) => ([
                                ...acc,
                                ...index > 0 ? ['properties', cur] : [cur],
                            ])
                            , [] as string[]
                        ).slice(1); // 去除开头的 body
                        const selectedBodyParam = get(
                            jsonSchema,
                            bodyParamPath
                        );
                        const newParamValue = getNewParamValue(key, selectedBodyParam);
                        return newParamValue;
                    } else {
                        const parameters: Record<string, BaseParam[]> = getFieldValue(
                            getOpenApiConfigParamsPath(toolIndex)
                        );
                        const name = getParamNameFromParamKey(key);
                        const parameter = parameters?.[paramsTabKey]?.find(item => name === item?.name);
                        return {
                            refParam: key,
                            ...parameter,
                            exampleValue: parameter?.example,
                        };
                    }
                }
            });
            setFieldValue(targetToolParamsListPath, mergedParamsList);
            // 处理完参数列表后，还需要处理参数配置
            const paramsConfigPath = getParamsConfigPath(toolIndex);
            const paramsConfigData = getFieldValue(paramsConfigPath);
            unRelatedParamKeys.forEach(key => {
                const paramFieldPathInConfig = getParamFieldPathInConfig(toolIndex, key, toolApiConfigDataRef.current);
                const targetParamValue = get(paramsConfigData, paramFieldPathInConfig);
                if (targetParamValue.type !== 'object') {
                    syncParamNameFieldValueToConfigTable(toolIndex, key, targetParamValue?.name);
                }
            });
        },
        [getFieldValue, setFieldValue, syncParamNameFieldValueToConfigTable]
    );

    const clearToolParams = useCallback(
        (toolIndex: number) => {
            const targetToolParamsListPath = getParamsListPath(toolIndex);
            const relatedParamsList = getFieldValue(targetToolParamsListPath);
            relatedParamsList.forEach((param: BaseParam) => {
                const paramKey = param.refParam;
                const paramValueFieldPathInConfigTable = getParamValueFieldPathInConfig(
                    toolIndex,
                    paramKey,
                    toolApiConfigDataRef.current
                );
                setFieldValue(paramValueFieldPathInConfigTable, '');
            });
            setFieldValue(targetToolParamsListPath, []);
        },
        [getFieldValue, setFieldValue]
    );

    return {
        toolParamsChanged,
        toolApiConfigDataRef,
        removeToolParam,
        addToolParam,
        syncParamNameFieldValueToConfigTable,
        batchSetToolParams,
        clearToolParams,
    };
});
