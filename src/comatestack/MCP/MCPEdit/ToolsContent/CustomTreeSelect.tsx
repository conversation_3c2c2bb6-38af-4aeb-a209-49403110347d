/* eslint-disable max-lines */
import {Flex, Input, Tooltip, TreeSelect, TreeSelectProps} from 'antd';
import {useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {IconClose, Tag} from '@panda-design/components';
import {IconSearch} from '@/icons/lucide';

const SearchIcon = styled(IconSearch)`
    color: #BFBFBF;
`;

const DropdownWrapper = styled(Flex)`
    .ant-5-select-tree-switcher{
        color: #BFBFBF;
    }
    .ant-5-select-tree-treenode{
        margin-bottom: 0 !important;
        padding-bottom: 8px !important;
        &: before{
            display: none !important;
        }
    }
`;

const CustomCloseIcon = styled(IconClose)`
    width: 10px;
    height: 10px;
    color: #848484;
    &:hover {
        color: #000;
    }
`;

const CloseIconWrapper = styled.span`
    display: inline-flex;
    cursor: pointer;
    margin-left: 4px;
`;

interface Props extends TreeSelectProps {
    noStyle?: boolean;
    onChange?: (value: string[]) => void;
}

// 特别注意，这个树使用了treeData中的key作为value。具体看，TreeSelect上的fieldNames属性。
export const CustomTreeSelect = (props: Props) => {
    const [searchText, setSearchText] = useState<string>('');
    const handleTreeData = useMemo(
        () => {
            if (searchText) {
                return props.treeData?.map(item => {
                    if ((item.title as string).toLowerCase().includes(searchText.toLowerCase())) {
                        return item;
                    } else if (item.children) {
                        // eslint-disable-next-line max-len
                        const children = item.children.filter(child => (child.title as string).toLowerCase().includes(searchText.toLowerCase()));
                        if (children.length > 0) {
                            return {...item, children};
                        }
                    }
                    return null;
                }).filter(Boolean);
            }
            return props.treeData;
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [searchText, props.treeData]
    );
    return (
        <TreeSelect
            popupMatchSelectWidth={240}
            {...props}
            {...props.noStyle
                ? {variant: 'borderless'}
                : {}

            }
            onChange={props.onChange}
            allowClear
            onDropdownVisibleChange={() => setSearchText('')}
            treeData={handleTreeData}
            showSearch={false}
            placeholder="请选择"
            maxTagTextLength={5}
            maxTagCount={1}
            treeDefaultExpandAll
            fieldNames={{value: 'key'}}
            tagRender={
                ({onClose, value, isMaxTag, label}) => {
                    if (value) {
                        return (
                            <Tag type="flat">
                                {value}
                                <CloseIconWrapper onClick={onClose}><CustomCloseIcon /></CloseIconWrapper>
                            </Tag>
                        );
                    } else if (isMaxTag) {
                        return (
                            <Tooltip>
                                <span><Tag type="flat">{label}</Tag></span>
                            </Tooltip>
                        );
                    }
                }
            }
            treeTitleRender={
                nodeData => {
                    const title = nodeData?.title as string || '';
                    const index = title.toLowerCase().indexOf(searchText.toLowerCase());
                    if (index === -1) {
                        return title;
                    }
                    return (
                        <span>
                            {title.slice(0, index)}
                            {
                                <span style={{color: '#0083FF'}}>{title.slice(index, index + searchText.length)}</span>
                            }
                            {title.slice(index + searchText.length)}
                        </span>
                    );
                }
            }
            dropdownRender={
                menu => {
                    return (
                        <DropdownWrapper vertical gap={8}>
                            <Input
                                value={searchText}
                                // eslint-disable-next-line max-lines
                                placeholder="请输入关键词检索"
                                prefix={<SearchIcon />}
                                onChange={e => {setSearchText(e.target.value);}}
                            />
                            {menu}
                        </DropdownWrapper>
                    );
                }
            }
        />
    );
};
