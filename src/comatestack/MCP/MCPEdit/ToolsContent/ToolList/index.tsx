/* eslint-disable max-lines */
import {Button} from '@panda-design/components';
import {Form, Tooltip} from 'antd';
import styled from '@emotion/styled';
import {memo, useEffect} from 'react';
import {loadMCPServerToolItem, useMCPServerToolItem} from '@/regions/mcp/mcpServer';
import {useMCPServerId} from '@/components/MCP/hooks';
import {transformBodySchemaToTableTreeData, transformRequestParamsToTableData} from '../../utils';
import {useActiveTool} from '../hooks';
import {useToolParamsConfigContext} from '../Provider/toolParamsConfigProvider';
import ToolListContent from './ToolListContent';

const StyledSideButton = styled(Button)`
    position: absolute !important;
    left: -24px !important;
    z-index: 1 !important;
    top: -20px !important;
    background-color: #fff !important;
    white-space: normal !important;
    width: 48px !important;
    height: 40px !important;
    font-size: 12px !important;
    line-height: 16px !important;
    word-wrap: break-word !important;
    box-shadow: 0px 1px 4px 0px #00000033 !important;
    border-radius: 0 20px 20px 0 !important;
    &:hover {
        background-color: #fff !important;
    }
`;

const TooltipTitleWrapper = styled.div`
    padding: 16px 12px;
    border-radius: 6px;
    box-shadow: 0px 2px 20px 0px #18181826;
    background: #fff;
`;

interface Props {
    displayAll: boolean;
    on: () => void;
    off: () => void;
}

const ToolsList = memo(({displayAll, on, off}: Props) => {
    const {toolApiConfigDataRef} = useToolParamsConfigContext();
    const mcpServerId = useMCPServerId();
    const {setFieldValue} = Form.useFormInstance();
    const {activeToolIndex} = useActiveTool();
    const activeTool = Form.useWatch(['tools', activeToolIndex]);
    const value = Form.useWatch(['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters']);
    window.console.log('tool list value', value);
    const toolItem = useMCPServerToolItem(activeTool?.id);
    useEffect(
        () => {
            if (activeTool?.id && !toolItem) {
                loadMCPServerToolItem({toolId: activeTool.id, mcpServerId: Number(mcpServerId)});
            }
        },
        [activeTool, mcpServerId, toolItem]
    );
    useEffect(
        () => {
            if (toolItem) {
                if (!activeTool.toolConf?.openapiConf?.parameters) {
                    setFieldValue(
                        ['tools', activeToolIndex, 'toolConf'],
                        toolItem.toolConf
                    );
                    const transformedValue = transformRequestParamsToTableData(
                        toolItem.toolConf?.openapiConf?.parameters
                    );
                    setFieldValue(
                        ['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters'],
                        transformedValue
                    );
                    const jsonSchema = toolItem.toolConf?.openapiConf?.requestBody?.jsonSchema;
                    // 有可能没body参数，所以给ref赋值两次
                    toolApiConfigDataRef.current = {...transformedValue};
                    if (jsonSchema) {
                        const bodyData = transformBodySchemaToTableTreeData(jsonSchema, []);
                        setFieldValue(
                            ['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters', 'body'],
                            bodyData
                        );
                        toolApiConfigDataRef.current.body = bodyData;
                    }
                }
                if (!activeTool.toolParams?.toolParams) {
                    setFieldValue(
                        ['tools', activeToolIndex, 'toolParams'],
                        {
                            ...toolItem.toolParams,
                            toolParams: toolItem.toolParams?.toolParams ?? [],
                        }
                    );
                }
            }
        },
        [activeTool, activeToolIndex, setFieldValue, toolApiConfigDataRef, toolItem]
    );

    if (!displayAll) {
        return (
            <Tooltip
                overlayInnerStyle={{width: 270, background: 'transparent', boxShadow: 'none'}}
                arrow={false}
                title={
                    <TooltipTitleWrapper>
                        <ToolListContent displayAll={displayAll} on={on} off={off} />
                    </TooltipTitleWrapper>
                }
            >
                <StyledSideButton
                    disabled={false}
                    style={{width: 48}}
                    type="text"
                    onClick={on}
                >
                    展开工具
                </StyledSideButton>
            </Tooltip>
        );
    }
    return (
        <ToolListContent displayAll={displayAll} on={on} off={off} />
    );
});

export default ToolsList;

