import {Form} from 'antd';
import {useCallback} from 'react';
import {v4} from 'uuid';
import {ApiDefinition, MCPToolItem} from '@/types/mcp/mcp';
import {useMCPServer} from '@/regions/mcp/mcpServer';
import {useMCPServerId} from '@/components/MCP/hooks';
import {transformBodySchemaToTableTreeData, transformRequestParamsToTableData} from '../../utils';

export const useAddTool = () => {
    const {setFieldValue} = Form.useFormInstance();
    const tools: MCPToolItem[] = Form.useWatch('tools');
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const addTool = useCallback(
        (value: any) => {
            if (value.apiInfos) {
                setFieldValue('tools', [
                    ...(tools || []),
                    ...value.apiInfos.map((apiInfo: ApiDefinition) => {
                        const {name, description, parameters, requestBody, path, method} = apiInfo;
                        const jsonSchema = requestBody?.jsonSchema;
                        return {
                            name,
                            description,
                            toolKey: v4(),
                            toolConf: {
                                openapiConf: {
                                    name,
                                    description,
                                    method,
                                    path,
                                    requestBody: requestBody,
                                    parameters: {
                                        ...transformRequestParamsToTableData(parameters),
                                        body: transformBodySchemaToTableTreeData(jsonSchema)?.children,
                                    },
                                },
                            },
                            toolParams: {
                                serverParams: mcpServer?.serverParams,
                            },
                        };
                    }),
                ]);
            } else {
                setFieldValue('tools', [...(tools || []), value]);
            }
        },
        [mcpServer?.serverParams, setFieldValue, tools]
    );
    return addTool;
};
