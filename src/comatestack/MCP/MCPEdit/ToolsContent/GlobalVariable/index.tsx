import {Path} from '@panda-design/path-form';
import {Table, TableColumnsType} from 'antd';
import {useMemo} from 'react';
import {BaseParam} from '@/types/mcp/mcp';
import GlobalVariableRefParamField from './GlobalVariableRefParamField';

interface Props {
    value?: BaseParam[];
    path: Path;
}


const GlobalVariable = ({value, path}: Props) => {
    const columns = useMemo<TableColumnsType<BaseParam>>(
        () => [
            {
                width: 80,
                title: '变量名称',
                dataIndex: 'name',
            },
            {
                title: '描述',
                dataIndex: 'description',
            },
            {
                title: '关联参数',
                width: 200,
                dataIndex: 'refParam',
                render: (refParam: string, record, index) => (
                    <GlobalVariableRefParamField path={[...path, index]} />
                ),
            },
            {
                width: 80,
                title: '类型',
                dataIndex: 'dataType',
            },
            {
                width: 80,
                title: '是否必须',
                dataIndex: 'required',
                render: (required: boolean) => (required ? '是' : '否'),
            },
        ],
        [path]
    );

    return (
        <Table rowKey="name" columns={columns} dataSource={value ?? []} pagination={{hideOnSinglePage: true}} />
    );
};

export default GlobalVariable;
