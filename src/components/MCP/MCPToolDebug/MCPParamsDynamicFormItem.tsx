import {Form, Input, InputNumber, Switch, DatePicker, FormItemProps, Typography} from 'antd';
import type {NamePath} from 'antd/es/form/interface';
import {Rule} from 'antd/lib/form';
import {useMemo} from 'react';

interface DynamicFormItemProps {
    name: NamePath;
    label?: string;
    description?: string;
    defaultValue?: any;
    type: string; // 'String' | 'Number' | 'Boolean' | 'Array' | 'Object' | 'Date' | 'Integer';
    required?: boolean;
}

const TextArea = Input.TextArea;

const integerRule: Rule = {
    message: '请输入integer类型的值',
    validator: (_, value) => {
        if (value === undefined || value === null || value === '') {
            return Promise.resolve();
        }
        if (Number.isInteger(Number(value))) {
            return Promise.resolve();
        }
        return Promise.reject(new Error('请输入integer类型的值'));
    },
};

export const MCPParamsDynamicFormItem = ({
    name,
    label,
    description,
    defaultValue,
    type,
    required = false,
}: DynamicFormItemProps) => {
    const formatType = useMemo(
        () => {
        // 接口有问题，有时候type是null，兼容一下
            const t = type?.toLowerCase();
            return t;
        },
        [type]
    );
    const rules = useMemo(
        () => {
            const baseRule = [];
            if (required) {
                baseRule.push({required: true, message: '该参数为必填项'});
            }
            if (formatType === 'integer') {
                baseRule.push(integerRule);
            }
            return baseRule;
        },
        [required, formatType]
    );
    const formItemProps: FormItemProps = useMemo(
        () => {
            return {
                name,
                label: <Typography.Text style={{width: '100px'}} ellipsis={{tooltip: label}}>{label}</Typography.Text>,
                initialValue: defaultValue,
                rules: rules,
            };
        },
        [defaultValue, label, name, rules]
    );
    const renderInput = useMemo(
        () => {
            switch (formatType) {
                case 'string':
                    return <Input placeholder={description} />;
                case 'number':
                    return <InputNumber style={{width: '100%'}} />;
                case 'integer':
                    return <InputNumber style={{width: '100%'}} />;
                case 'boolean':
                    return <Switch checkedChildren="true" unCheckedChildren="false" />;
                case 'array':
                case 'object':
                    return <TextArea placeholder={description} />;
                case 'date':
                    return <DatePicker showTime style={{width: '100%'}} placeholder={description} />;
                default:
                    return <Input placeholder={description} />;
            }
        },
        [formatType, description]
    );

    return (
        <Form.Item
            {...formItemProps}
            required={required}
            labelAlign="left"
            colon={false}
        >{renderInput}
        </Form.Item>
    );
};
