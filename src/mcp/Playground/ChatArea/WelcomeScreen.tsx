import React from 'react';
import styled from '@emotion/styled';
import {Flex} from 'antd';
import bannerUrl from '@/assets/mcp/welcomeBg.png';
import {
    IconStep01,
    IconStep02,
    IconStep03,
    IconStep04,
    IconElipsis,
} from '../../../icons/mcp';
import {mcpPlaygroundStyles, stepTexts} from '../constants/styles';

const WelcomeTitle = styled.h1`
    font-weight: ${mcpPlaygroundStyles.typography.title.fontWeight};
    font-size: ${mcpPlaygroundStyles.typography.title.fontSize};
    line-height: ${mcpPlaygroundStyles.typography.title.lineHeight};
    color: ${mcpPlaygroundStyles.typography.title.color};
`;

const SubTitle = styled.p`
    font-weight: ${mcpPlaygroundStyles.typography.subtitle.fontWeight};
    font-size: ${mcpPlaygroundStyles.typography.subtitle.fontSize};
    line-height: ${mcpPlaygroundStyles.typography.subtitle.lineHeight};
    color: ${mcpPlaygroundStyles.typography.subtitle.color};
    margin-top: ${mcpPlaygroundStyles.typography.subtitle.marginTop};
    margin-bottom: ${mcpPlaygroundStyles.typography.subtitle.marginBottom};
`;

const StepDescription = styled.p`
    font-size: ${mcpPlaygroundStyles.typography.stepDescription.fontSize};
    color: ${mcpPlaygroundStyles.typography.stepDescription.color};
    line-height: ${mcpPlaygroundStyles.typography.stepDescription.lineHeight};
`;

const WelcomeScreen = () => {
    const stepIconStyle = mcpPlaygroundStyles.icons.stepIconSize;
    const ellipsisIconStyle = {
        fontSize: mcpPlaygroundStyles.icons.ellipsisIconSize,
    };
    const stepIcons = [IconStep01, IconStep02, IconStep03, IconStep04];

    return (
        <Flex
            vertical
            style={{
                padding: mcpPlaygroundStyles.layout.containerPadding,
                backgroundImage: `url(${bannerUrl})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                minHeight: '400px',
                position: 'relative',
                zIndex: 1,
            }}
        >
            <WelcomeTitle>欢迎来到MCP Playground</WelcomeTitle>
            <SubTitle>简单四步 开始体验海量MCP工具</SubTitle>

            <Flex justify="space-between" align="center">
                {stepIcons.map((StepIcon, index) => (
                    <React.Fragment key={index}>
                        <StepIcon style={stepIconStyle} />
                        {index < stepIcons.length - 1 && (
                            <IconElipsis style={ellipsisIconStyle} />
                        )}
                    </React.Fragment>
                ))}
            </Flex>

            <Flex
                justify="space-between"
                align="center"
                style={{position: 'relative'}}
            >
                {stepTexts.map((text, index) => (
                    <StepDescription
                        key={index}
                        style={{
                            position: 'absolute',
                            ...mcpPlaygroundStyles.positioning.stepDescriptions[
                                index
                            ],
                        }}
                    >
                        {text}
                    </StepDescription>
                ))}
            </Flex>
        </Flex>
    );
};

export default WelcomeScreen;
