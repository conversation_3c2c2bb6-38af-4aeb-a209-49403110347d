export interface ChatMessageInputProps {
    disabled?: boolean;
    disabledReason?: string;
    onSend: (content: string) => Promise<void>;
    onStop?: () => void;
    isGenerating?: boolean;
    placeholder?: string;
}

export const getInputRef = (ref: any) => {
    const textArea = ref?.resizableTextArea?.textArea;
    return textArea || null;
};

export const shouldBeMultiLine = (content: string): boolean => {
    const lines = content.split('\n');
    const hasMultipleLines = lines.length > 1;
    const hasLongLine = lines.some(line => line.length > 60);
    return hasMultipleLines || hasLongLine;
};

export const createInputProps = (
    content: string,
    disabled: boolean,
    disabledReason: string,
    placeholder: string,
    isMultiLine: boolean
) => ({
    value: content,
    placeholder: disabled ? disabledReason : placeholder,
    disabled,
    autoSize: isMultiLine ? {minRows: 1, maxRows: 5} : {minRows: 1, maxRows: 1},
    isMultiLine,
});
