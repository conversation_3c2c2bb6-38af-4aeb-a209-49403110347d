import {Input} from 'antd';
import styled from '@emotion/styled';
import {
    UI_DIMENSIONS,
    UI_COLORS,
} from '../constants';

export const InputComponent = styled(Input.TextArea, {
    shouldForwardProp: prop => prop !== 'isMultiLine',
})<{ isMultiLine: boolean }>`
    resize: none !important;
    flex: 1;
    font-size: ${UI_DIMENSIONS.FONT_SIZE_MEDIUM}px;
    weight: 400;
    line-height: 22px;
    border: none !important;
    box-shadow: none !important;
    padding-bottom: 14px;

    &:focus,
    &:focus-within,
    &.ant-input-focused {
        border: none !important;
        box-shadow: none !important;
        outline: none !important;
    }

    .ant-input {
        padding: 0;
        border: none !important;
        box-shadow: none !important;
        background: transparent;

        &:focus,
        &:focus-within {
            border: none !important;
            box-shadow: none !important;
            outline: none !important;
        }
    }

    &:disabled {
        background: ${UI_COLORS.BACKGROUND_DISABLED} !important;
        color: ${UI_COLORS.TEXT_TERTIARY};
    }

    ::-webkit-scrollbar {
        width: ${UI_DIMENSIONS.SCROLLBAR_WIDTH}px;
    }

    ::-webkit-scrollbar-thumb {
        background: ${UI_COLORS.SCROLLBAR_THUMB};
        border-radius: ${UI_DIMENSIONS.BORDER_RADIUS_SMALL}px;
    }
`;
